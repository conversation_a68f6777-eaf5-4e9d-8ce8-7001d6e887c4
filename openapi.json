{"openapi": "3.1.0", "info": {"title": "Bibliographic Items Tagging API", "description": "API for automatic tagging of bibliographic items using ML-based models", "version": "0.2.0"}, "paths": {"/api/generate_tags": {"post": {"summary": "Generate Tags", "description": "Generate tags for items.", "operationId": "generate_tags_api_generate_tags_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateTagsRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ArticleResult"}, "type": "array", "title": "Response Generate Tags Api Generate Tags Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/generate_tags_test": {"post": {"summary": "Test Tags", "description": "Test endpoint that returns mock tags for development and testing purposes.", "operationId": "test_tags_api_generate_tags_test_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateTagsRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ArticleResult"}, "type": "array", "title": "Response Test Tags Api Generate Tags Test Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/tags/all": {"get": {"summary": "Get All Tags", "description": "Return all tags from the requested tag collection.", "operationId": "get_all_tags_api_tags_all_get", "parameters": [{"name": "collection", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "default_collection", "title": "Collection"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AllTagsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/health": {"get": {"summary": "Health Check", "description": "Simple health check endpoint to confirm that the API is running.", "operationId": "health_check_api_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}}}}}, "components": {"schemas": {"AllTagsResponse": {"properties": {"tags": {"items": {"type": "object"}, "type": "array", "title": "Tags", "description": "List of tags in the collection"}}, "type": "object", "required": ["tags"], "title": "AllTagsResponse", "description": "Response model for all_tags endpoint."}, "ArticleItem": {"properties": {"index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Index", "description": "Item index", "default": 0}, "key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Key", "description": "Item key/ID", "default": ""}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title", "description": "Item title", "default": ""}, "abstract": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Abstract", "description": "Item abstract", "default": ""}}, "type": "object", "title": "ArticleItem", "description": "Model for individual article item in request."}, "ArticleResult": {"properties": {"index": {"type": "integer", "title": "Index", "description": "Item index"}, "key": {"type": "string", "title": "Key", "description": "Item key/ID"}, "title": {"type": "string", "title": "Title", "description": "Item title"}, "tags": {"$ref": "#/components/schemas/TagsResult", "description": "Generated tags"}}, "type": "object", "required": ["index", "key", "title", "tags"], "title": "ArticleResult", "description": "Model for individual item result."}, "GenerateTagsRequest": {"properties": {"items": {"items": {"$ref": "#/components/schemas/ArticleItem"}, "type": "array", "title": "Items", "description": "List of items to process"}, "collection": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Collection", "description": "Collection to use for matching", "default": "default_collection"}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model", "description": "Model to use for processing", "default": "gpt-4o-mini"}}, "type": "object", "required": ["items"], "title": "GenerateTagsRequest", "description": "Request model for generate_tags endpoint."}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HealthResponse": {"properties": {"status": {"type": "string", "title": "Status", "description": "API status"}}, "type": "object", "required": ["status"], "title": "HealthResponse", "description": "Response model for health check endpoint."}, "TagsResult": {"properties": {"matched_tags": {"items": {"type": "string"}, "type": "array", "title": "Matched Tags", "description": "Matched tags from tag pool"}, "concept_tags": {"items": {"type": "string"}, "type": "array", "title": "Concept Tags", "description": "Concept/keyword tags"}, "person_org_tags": {"items": {"type": "string"}, "type": "array", "title": "Person Org Tags", "description": "Person and organization tags"}, "time_place_tags": {"items": {"type": "string"}, "type": "array", "title": "Time Place Tags", "description": "Time and place tags"}}, "type": "object", "title": "TagsResult", "description": "Model for tags result."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}