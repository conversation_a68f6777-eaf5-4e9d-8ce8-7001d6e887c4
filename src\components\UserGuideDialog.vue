<template>
  <el-dialog 
    v-model="visible" 
    title="USER GUIDE" 
    :width="screenIsPortrait ? '100%' : '80%'"
    center
    class="user-guide-dialog"
    @update:model-value="handleVisibilityChange"
  >
    <div class="user-guide-content">
      <h2><strong>Bibliographic Items Tagging Tool User Guide (Ver 0.5.1)</strong></h2>

      <h4>Author: <PERSON><PERSON> (https://www.yizehu.com)</h4>

      <p>The Bibliographic Items Tagging Tool is an AI-driven web application designed to <strong>collaborate with the user</strong> to match bibliographic items to tags in a predefined tag pool (e.g., IsisCB's tag pool, etc). The tool allows users to import data from sources like Zotero or local files, review and edit the matched tags, and then export the tagged items in various formats or save them back to Zotero. This guide will walk you through the process of importing your data, generating tags, reviewing them, and exporting the final results.</p>

      <h3><strong>Quick Overview</strong></h3>

      <p>The workflow is broken down into three main steps, which are laid out in the application:</p>

      <ol>
        <li><strong>Import Items:</strong> Add your bibliographic data from a local file, from Zotero, or by manual entry.</li>
        <li><strong>Review and Edit Tags:</strong> Use the AI to generate tags, then review and refine them.</li>
        <li><strong>Export Result:</strong> Download your newly tagged data in various formats or save it back to Zotero.</li>
      </ol>

      <p>You can access <strong>Advanced Settings</strong> using the floating settings button (⚙️) on the right side of the screen.</p>

      <h3 class="privacy-notice"><strong>! Privacy Notice</strong></h3>
      <p>This tool itself does not collect or store any user data. The title and abstract text are sent to the server for tagging. Generative AI model hosted by OpenAI is used for tagging if <code>gpt-4o-mini</code> is selected as the tag generator, and the title and abstract text will be visible to OpenAI and may be used by OpenAI for training purposes. Any other data you uploaded or entered, including the files and editing of tags, are stored and processed locally in your browser.</p>

      <hr>

      <h2><strong>Step 1: Import and Submit Bibliographic Items</strong></h2>

      <p>First, you need to import the items you want to tag. You have three options, presented in a collapsible menu.</p>

      <h3><strong>Method 1: Upload from File (RDF or CSV)</strong></h3>

      <ul>
        <li>Click the <strong>Upload File</strong> button under the "Upload from File" section.</li>
        <li>Select a RDF or CSV file from your computer. <strong>A RDF file exported from Zotero is recommended.</strong></li>
        <li><strong>For RDF files:</strong> The tool will parse the file to find bibliographic items. If it's a Zotero RDF export, it will be handled accordingly.</li>
        <li><strong>For CSV files exported from Zotero:</strong> Keep the <strong>"CSV exported from Zotero"</strong> box checked (default) if your CSV file has a standard header row. The tool will automatically map fields like <code>Title</code>, <code>Abstract Note</code>, <code>Author</code>, etc. Uncheck this box if your CSV is a simple two-column file without a header. The tool will treat the first column as the <code>title</code> and the second as the <code>abstract</code>.
        </li>
      </ul>

      <h3><strong>Method 2: Import from Zotero</strong></h3>

      <p>This option allows you to save tags back to the original Zotero items later.</p>

      <ul>
        <li><strong>Library ID:</strong> Enter your Zotero User ID (for a personal library) or Group ID (for a shared group library).
          <ul>
            <li>You can find your <strong>User ID</strong> in your Zotero security settings.</li>
            <li>The <strong>Group ID</strong> is in the URL of your group's library page on the Zotero website.</li>
          </ul>
        </li>
        <li><strong>API Key:</strong> Create and enter a Zotero API key from your Zotero security settings. This is required for the tool to access your library.</li>
        <li><strong>Tag:</strong> Enter a tag that you have applied in Zotero to the items you wish to import (e.g., <code>to-be-tagged</code>). The tool will fetch all items with this specific tag.</li>
        <li>Click <strong>Fetch Zotero Items</strong>. The tool will connect to your library and import the relevant items, showing a preview when complete.</li>
      </ul>

      <h3><strong>Method 3: Manually Add Items</strong></h3>

      <p>For manual additions or testing, you can add (and delete) items one by one. Items can also be manually added after importing from a file or Zotero.</p>

      <ul>
        <li>Enter the <strong>Title</strong> and <strong>Abstract</strong> in the provided text boxes.</li>
        <li>Click <strong>Add to List</strong>.</li>
        <li>Use the <strong>Remove Last Item</strong> and <strong>Undo Remove</strong> buttons to manage your manually added list.</li>
      </ul>

      <h3><strong>Previewing Items to be Processed</strong></h3>

      <p>Once you've imported items, a preview card will appear showing the total count and the titles of the first and last few items in your list. The source of the data (e.g., "Zotero," "local file") will also be displayed.</p>

      <h3><strong>Submitting Your Items for Tagging</strong></h3>

      <p>After importing your items, you can generate tags for them.</p>

      <ul>
        <li><strong>Tag Items:</strong> Click the green <strong>Tag Items</strong> button.</li>
        <li><strong>Batch Size:</strong> The tool processes items in batches. A suggested batch size is provided, but you can set your own number. Smaller batches provide more frequent progress updates; larger batches can be slightly faster overall.</li>
        <li><strong>Processing:</strong> A loading message will appear showing the elapsed time. Batches are processed one by one, and you will receive a success message as each batch is completed.</li>
      </ul>

      <hr>

      <h2><strong>Step 2: Review and Edit Matched Tags</strong></h2>

      <h3><strong>Reviewing and Editing</strong></h3>

      <p>Once the first batch is processed, the results will appear in the "Step 2. Review and Edit Matched Tags" section. No need to wait for all batches to finish as you can start reviewing and editing the tags as soon as the first batch is ready. The tool will automatically process and display the next batch in the background.</p>

      <ul>
        <li><strong>Expand/Collapse:</strong> You can expand each item to see its details or use the <strong>Expand All</strong> / <strong>Collapse All</strong> button for convenience. You may also use a floating button on the bottom right corner with the same function.</li>
        <li><strong>Matched Tags:</strong> These are the tags to be added to and exported for the item. You can:
          <ul>
            <li><strong>Deselect a tag:</strong> Simply click on a tag to deselect it. It will become grayed out and struck through, marking it for exclusion from the final export. Click it again to re-activate it.</li>
            <li><strong>Add a tag from the tag pool:</strong>
              <ul>
                <li>Start typing in the input box ("<em>Type to search or Enter to add...</em>").</li>
                <li>A dropdown will appear with suggestions from the main tag pool. You can click a suggestion to add it. Use spaces to search for tags containing multiple terms (e.g., "China Japan" finds tags with both words). Enable enhanced display in Advanced Settings to see citation counts and categories.</li>
              </ul>
            </li>
            <li><strong>Add a new tag not in the pool:</strong>To add a completely new custom tag, type your tag and press <strong>Enter</strong>.</li>
            <li><strong>Remove an added tag:</strong> Click the 'x' on any newly added tag to remove it.</li>
          </ul>
        </li>
        <li><strong>Reference Tags (Concept, Person/Org, Time/Place):</strong> These are the original keywords extracted by the AI for your reference. To add one to your main "Matched Tags" list, you can either <strong>double-click</strong> it or <strong>drag-and-drop</strong> it into the "Matched Tags" area.</li>
        <li><strong>Review Status: </strong>Check the <strong>I have reviewed this item</strong> checkbox once you have finished reviewing and editing the tags for an item. Items that have been reviewed will have a green checkmark.</li>

      </ul>

      <hr>

      <h2><strong>Step 3: Export Your Results</strong></h2>

      <p>After reviewing and editing the tags, you can export your work.</p>

      <h3><strong>Downloading Files</strong></h3>

      <ol>
        <li><strong>Select Format:</strong> Choose your desired file format: <strong>RDF</strong> (default), <strong>RIS</strong>, <strong>BibTeX</strong>, or <strong>CSV</strong>.</li>
        <li><strong>Tag Formatting Options (Optional):</strong> Before downloading, you can expand this section to customize how your tags are formatted and what extra tags are added to each item.
          <ul>
            <li><strong>Add prefix/suffix to customized tags:</strong> Adds a special string (e.g., <code>[NEW]</code>) to tags that you created, which were not part of the tag pool.</li>
            <li><strong>Add metadata suffix to matched tags:</strong> Appends metadata to tags, such as the tag's unique ID from the tag pool (e.g., <code>my-tag [record_id]</code>).</li>
            <li><strong>Add custom suffix:</strong> Appends a custom string (e.g., <code>[IsisCBtag]</code>) to all matched tags.</li>
            <li><strong>Add an extra tag for all processed items:</strong> Adds a single, consistent tag (e.g., <code>processed-by-tagger</code>) to every item that was processed.</li>
          </ul>
        </li>
        <li><strong>Export Options:</strong> For each file format, you can click the <strong>Options</strong> button to customize the export. This allows you to select which data fields (e.g., Title, Author, Volume) to include in the exported file. If the bibliographic items are imported from a Zotero RDF file, you will have an option to add the active tags back to the original file, which preserves the original file structure and information. This is the default behavior.</li>
        <li><strong>Download:</strong> Click the <strong>Download As</strong> button to save the file to your computer. The file can then be imported back into Zotero or used in other applications.</li>
      </ol>

      <h3><strong>Saving to Zotero Directly</strong></h3>

      <ul>
        <li>If you imported your data from Zotero, the <strong>Save Matched Tags to Zotero</strong> button will be enabled.</li>
        <li>Clicking this will update the original items in your Zotero library by adding the active tags. It adds the new tags without touching any other fields of the item. The tag used for fetching the items from Zotero (such as <code>to-be-tagged</code>) will be removed.</li>
      </ul>

      <hr>

      <h2><strong>Advanced Settings</strong></h2>

      <p>Click the floating gear icon (⚙️) to open the Advanced Settings drawer. Here you can:</p>

      <ul>
        <li><strong>Select Tag Generator:</strong> Choose the method used for generating tags. <code>gpt-4o-mini</code> is the default which offers good quality. <code>pytextrank</code> is much faster but much less accurate.</li>
        <li><strong>Customize API's URL:</strong> If you are hosting the backend service yourself or using a different endpoint, you can change the API URLs here.</li>
        <li><strong>Change UI Options:</strong>
          <ul>
            <li><strong>Show counts and categories in tag searching:</strong> Tag suggestions show citation counts and color-coded categories (e.g. Concept, Persons, Institutions, Times and Places). This option is enabled by default. You may disable it for better performance.</li>
          </ul>
        </li>
      </ul>

      <hr>

      <p>(Updated: 2025-7-20)</p>
    </div>
    
    <template #footer>
      <el-button type="primary" @click="closeDialog">Close</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElDialog, ElButton } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  screenIsPortrait: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// Local state
const visible = ref(props.modelValue)

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue
})

// Handle visibility changes
const handleVisibilityChange = (newValue) => {
  emit('update:modelValue', newValue)
}

// Close dialog method
const closeDialog = () => {
  visible.value = false
  emit('update:modelValue', false)
}
</script>

<style scoped>
.user-guide-content {
  max-width: 800px;
  overflow-y: auto;
  padding: 20px;
  line-height: 1.6;
  font-size: 16px;
}

.user-guide-content h1 {
  color: var(--el-color-primary);
  font-size: 24px;
  margin-bottom: 16px;
  border-bottom: 2px solid var(--el-color-primary-light-8);
  padding-bottom: 8px;
}

.user-guide-content h2 {
  color: var(--el-color-primary);
  font-size: 20px;
  margin-top: 24px;
  margin-bottom: 12px;
}

.user-guide-content h3 {
  color: var(--el-text-color-primary);
  font-size: 16px;
  margin-top: 20px;
  margin-bottom: 10px;
  font-weight: bold;
}

.user-guide-dialog .user-guide-content h3.privacy-notice {
  color: red !important;
}

.user-guide-content h4 {
  color: var(--el-text-color-primary);
  font-size: 14px;
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: bold;
}

.user-guide-content ul {
  margin: 8px 0;
  padding-left: 20px;
  list-style-type: disc;
}

.user-guide-content ol {
  margin: 4px 0;
  padding-left: 20px;
  list-style-type: decimal;
}



.user-guide-content ol > li {
  list-style-type: decimal;
}

.user-guide-content code {
  background-color: var(--el-color-info-light-9);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.user-guide-content pre {
  background-color: var(--el-color-info-light-9);
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 12px 0;
}

.user-guide-content pre code {
  background: none;
  padding: 0;
}

.user-guide-content strong {
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.user-guide-content em {
  font-style: italic;
}

.user-guide-content hr {
  border: none;
  border-top: 1px solid var(--el-border-color);
  margin: 20px 0;
}
</style>
